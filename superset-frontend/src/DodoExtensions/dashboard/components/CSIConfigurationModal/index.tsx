import { useState, useRef, lazy, Suspense } from 'react';
import { styled, t } from '@superset-ui/core';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import ModalTrigger, { ModalTriggerRef } from 'src/components/ModalTrigger';
import Button from 'src/components/Button';
import { bootstrapData } from 'src/preamble';
import Tabs from 'src/components/Tabs/Tabs';
import Checkbox from 'src/components/Checkbox';
import Loading from 'src/components/Loading';
import { getChartOptions, areArraysEqual } from './utils';

const EchartLazy = lazy(
  () => import('src/../plugins/plugin-chart-echarts/src/components/Echart'),
);

const locale = bootstrapData?.common?.locale || 'en';

const TabPane = styled(Tabs.TabPane)`
  padding: ${({ theme }) => theme.gridUnit * 4}px 0px;
`;

const Label = styled.p`
  margin: 0;
`;

const CheckboxControl = styled.div`
  margin-left: ${({ theme }) => theme.gridUnit * 4}px;
  margin-bottom: ${({ theme }) => theme.gridUnit * 2}px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

enum Env {
  Standalone = 'standalone',
  Plugin = 'plugin',
}

type Tab = 'configuration' | 'collection';

interface CSIData {
  envs: Env[];
  data: any[];
}

const CSIConfigurationModal = ({
  dashboardId,
  dashboardTitle,
  dashboardTitleRU,
}: {
  dashboardId: number;
  dashboardTitle: string;
  dashboardTitleRU: string;
}) => {
  const [activeTab, setActiveTab] = useState<Tab>('configuration');
  const [isLoading, setIsLoading] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [data, setData] = useState<CSIData | null>(null);
  const [isTableViz, setIsTableViz] = useState(false);
  const [newEnvs, setNewEnvs] = useState<Env[]>([]);
  const modalRef = useRef<ModalTriggerRef['current']>(null);
  const { addDangerToast, addSuccessToast } = useToasts();

  const localisedDashboardTitle =
    locale === 'ru' ? dashboardTitleRU : dashboardTitle;

  const getCSIInfo = async () => {
    setIsLoading(true);
    try {
      // const resp = await SupersetClient.get({
      //   endpoint: `/api/v1/dashboard/${dashboardId}`,
      // });

      await new Promise(resolve => {
        setTimeout(resolve, 2000);
      });

      setData({ envs: [], data: [] });
    } catch {
      addDangerToast('Failed to fetch CSI info');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    modalRef.current?.close();
  };

  const toggleCSI = async () => {
    try {
      setIsApplying(true);
      // await SupersetClient.put({
      //   endpoint: `/api/v1/dashboard/${dashboardId}`,
      //   jsonPayload: {
      //     is_csi_enabled: true,
      //   },
      // });
      await new Promise(resolve => {
        setTimeout(resolve, 2000);
      });

      addSuccessToast(
        t('CSI is enabled for dashboard %s', localisedDashboardTitle),
      );

      handleClose();
    } catch (error) {
      addDangerToast(
        t(
          'An error occurred while enabling CSI for dashboard %s. Please try again.',
          localisedDashboardTitle,
        ),
      );
    } finally {
      setIsApplying(false);
    }
  };

  const handleChangeTab = (key: Tab) => {
    setActiveTab(key);
  };

  const handleEnvChange = (env: Env) => (checked: boolean) => {
    if (checked) {
      setNewEnvs(prev => [...prev, env]);
    } else {
      setNewEnvs(prev => prev.filter(e => e !== env));
    }
  };

  const body = (
    <>
      {isLoading && <Loading />}
      {data && (
        <Tabs activeKey={activeTab} onChange={handleChangeTab}>
          <TabPane key="configuration" tab={t('Configuration')}>
            <>
              <p>{t('Set up feedback collection for:')}</p>
              <CheckboxControl>
                <Checkbox
                  checked={newEnvs.includes(Env.Standalone)}
                  onChange={handleEnvChange(Env.Standalone)}
                />
                <Label>Standalone</Label>
              </CheckboxControl>
              <CheckboxControl>
                <Checkbox
                  checked={newEnvs.includes(Env.Plugin)}
                  onChange={handleEnvChange(Env.Plugin)}
                />
                <Label>Plugin</Label>
              </CheckboxControl>
            </>
          </TabPane>
          <TabPane key="collection" tab={t('Data collection')}>
            <Suspense fallback={<Loading />}>
              {isTableViz ? <EchartLazy {...getChartOptions()} />}
            </Suspense>
          </TabPane>
        </Tabs>
      )}
    </>
  );

  return (
    <ModalTrigger
      ref={modalRef}
      triggerNode={<span>{t('CSI configuration')}</span>}
      modalTitle={t('CSI dashboard configuration')}
      maxWidth="450px"
      beforeOpen={getCSIInfo}
      onExit={() => {
        setIsLoading(false);
        setIsApplying(false);
        setData(null);
        setNewEnvs([]);
      }}
      modalBody={body}
      modalFooter={
        <>
          {activeTab === 'configuration' && (
            <Button
              buttonStyle="primary"
              buttonSize="small"
              onClick={toggleCSI}
              disabled={isApplying || areArraysEqual(data?.envs || [], newEnvs)}
              loading={isApplying}
            >
              {t('Apply')}
            </Button>
          )}
          <Button
            onClick={handleClose}
            buttonSize="small"
            disabled={isApplying}
          >
            {t('Close')}
          </Button>
        </>
      }
    />
  );
};

export default CSIConfigurationModal;
