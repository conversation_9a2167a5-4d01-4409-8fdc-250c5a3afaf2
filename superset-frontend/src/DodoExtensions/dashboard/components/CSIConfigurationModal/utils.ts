export const areArraysEqual = (arr1: string[], arr2: string[]): boolean => {
  const set1 = new Set(arr1);
  const set2 = new Set(arr2);

  if (set1.size !== set2.size) return false;

  for (const item of set1) {
    if (!set2.has(item)) return false;
  }

  return true;
};
interface Feedback {
  date: string;
  rating: number;
  comment: string;
  is_anonymous: boolean;
  is_plugin: boolean;
}

export const getChartOptions = () => {
  const data = [];
  return {
    width: 568,
    height: 400,
    echartOptions: {
      xAxis: {
        type: 'category',
        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 5,
      },
      legend: {
        data: ['Plugin', 'Standalone'],
      },
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      series: [
        {
          name: 'Plugin',
          data: [1, 3, 2, 4, 3, 5],
          type: 'bar',
          itemStyle: {
            color: '#20A7C9', // Цвет для Plugin
          },
        },
        {
          name: 'Standalone',
          data: [2, 4, 1, 3, 5, 2],
          type: 'bar',
          itemStyle: {
            color: '#F2A24C', // Цвет для Standalone
          },
        },
      ],
    },
    selectedValues: {},
    refs: {},
  };
};
